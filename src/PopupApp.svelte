<script lang="ts">
  import { onMount } from 'svelte'

  let donateButton: HTMLButtonElement
  let downloadHistoryButton: HTMLButtonElement

  onMount(() => {
    // Add event listeners for the buttons
    if (donateButton) {
      donateButton.addEventListener('click', handleDonate)
    }
    if (downloadHistoryButton) {
      downloadHistoryButton.addEventListener('click', handleDownloadHistory)
    }
  })

  function handleDonate() {
    console.log("Donate!");
    chrome.tabs.create({
      url: "https://donate.stripe.com/bJe4gAeo4gy28Ge39B24000",
      active: true
    });
  }

  async function handleDownloadHistory() {
    const { SearchHistory = [] } =
      await chrome.storage.local.get({ SearchHistory: [] });

    if (SearchHistory.length === 0) return;

    const content = SearchHistory.join("\n");

    const blob = new Blob([content], { type: "text/plain" });
    const url = URL.createObjectURL(blob);

    const a = document.createElement("a");
    a.href = url;
    a.download = "ebay_search_history.txt";
    document.body.appendChild(a);
    a.click();
    a.remove();

    URL.revokeObjectURL(url);
  }
</script>

<main>
  <h1 id="text">Please consider donating!</h1>
  <h5 id="keybind-text">Keybind: Ctrl+Y</h5>
  <div id="button-container">
    <button bind:this={donateButton} id="donate-button">Donate</button>
    <button bind:this={downloadHistoryButton} id="download-history">Download History</button>
  </div>
</main>

<style>
  :global(:root) {
    --blue-light: #e0f4ff;
    --blue-base:  #4a90e2;
    --blue-dark:  #005bb5;
  }

  :global(*) {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  :global(body) {
    width: 300px;
    padding: 16px;
    font-family: Arial, sans-serif;
    background: var(--blue-light);
    color: var(--blue-dark);
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  main {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  #text {
    font-size: 1.2em;
    margin-bottom: 16px;
    text-align: center;
  }

  h5 {
    font-size: 15px;
    margin-top: -15px;
    color: var(--blue-dark);
  }

  #button-container {
    display: flex;
    gap: 8px;
    width: 100%;
  }

  button {
    flex: 1;
    padding: 10px;
    margin-top: 10px;
    border: none;
    border-radius: 15px;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.2s;
    color: #fff;
    background: var(--blue-base);
  }

  button:hover {
    background: var(--blue-dark);
  }

  #download-history {
    background: #0071c1;
  }

  #download-history:hover {
    background: #004f8a;
  }
</style>
