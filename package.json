{"name": "productsearch", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"copy": "cp -r src/ app/", "copy:watch": "chokidar \"src/**\" -c \"npm run copy\" --initial", "build": "tsc -p tsconfig.json && npm run copy", "watch": "concurrently \"tsc -p tsconfig.json --watch\" \"npm run copy:watch\"", "dev:popup": "vite", "build:popup": "vite build", "build:extension": "npm run build && npm run build:popup", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.json"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@sveltejs/vite-plugin-svelte": "^5.1.1", "@tsconfig/svelte": "^5.0.4", "@types/chrome": "^0.1.1", "@types/node": "^24.0.14", "chokidar-cli": "^3.0.0", "concurrently": "^9.2.0", "svelte": "^5.36.13", "svelte-check": "^4.3.0", "typescript": "^5.8.3", "vite": "^6.0.0"}}